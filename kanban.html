<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Kanban Tracker</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- SortableJS for Drag and Drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    
    <!-- Canvas Confetti for celebrations -->
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>

    <style>
        /* --- Custom Styling for a Modern Look --- */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f1f5f9; /* slate-100 */
        }

        /* Kanban column scrollbar styling */
        .kanban-column-body::-webkit-scrollbar { width: 8px; }
        .kanban-column-body::-webkit-scrollbar-track { background: #e2e8f0; border-radius: 10px; }
        .kanban-column-body::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 10px; }
        .kanban-column-body::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Drag-and-drop placeholder style */
        .sortable-ghost {
            background: #dbeafe; /* blue-200 */
            opacity: 0.6;
            border-radius: 0.5rem;
        }
        .sortable-chosen { cursor: grabbing; }

        /* Improved task card styling */
        .task-card {
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            border-color: #cbd5e1;
        }
        .task-card .card-controls {
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        .task-card:hover .card-controls {
            opacity: 1;
        }

        /* Better modal styling */
        .modal {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        .modal.hidden {
            opacity: 0;
            transform: scale(0.95);
            pointer-events: none;
        }
        
        /* Spinner for AI actions */
        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border-left-color: #fff;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Improved column headers */
        .kanban-column-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid #e2e8f0;
        }

        /* Better button styling */
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
            box-shadow: 0 4px 6px -1px rgb(79 70 229 / 0.1);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #4338ca 0%, #5b21b6 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 12px -2px rgb(79 70 229 / 0.2);
        }
    </style>
</head>
<body class="bg-slate-100 text-slate-800">

    <div id="app-container" class="p-4 md:p-8 min-h-screen flex flex-col">
        <!-- Header -->
        <header class="mb-8 flex flex-col md:flex-row justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-slate-900">AI-Powered Kanban Tracker</h1>
                <p id="auth-status" class="text-sm text-slate-500 mt-1">Connecting...</p>
            </div>
            <button id="addTaskBtn" class="btn-primary mt-4 md:mt-0 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                Add New Task
            </button>
        </header>

        <!-- Kanban Board -->
        <main id="kanban-board" class="flex-grow grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- To Do Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-rose-100 text-rose-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">To Do</span>
                            <span id="todo-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="todo" data-status="todo" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
            <!-- In Progress Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">In Progress</span>
                            <span id="inprogress-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="inprogress" data-status="inprogress" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
            <!-- Done Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-emerald-100 text-emerald-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">Done</span>
                            <span id="done-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="done" data-status="done" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
        </main>
        
        <footer class="text-center text-xs text-slate-400 mt-8">
            <p>Your User ID (for sharing): <span id="user-id-display" class="font-mono bg-slate-200 text-slate-600 p-1 rounded"></span></p>
        </footer>
    </div>

    <!-- Improved Task Modal -->
    <div id="taskModal" class="modal fixed inset-0 bg-slate-900/60 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-2xl p-6 w-full max-w-lg mx-4 border border-slate-200">
            <h3 class="text-2xl font-bold mb-6 text-slate-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create a New Task
            </h3>
            <textarea id="taskInput" class="w-full border-2 border-slate-200 rounded-lg p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all resize-none" rows="4" placeholder="Enter a task description or a big goal to break down..."></textarea>
            <div class="flex flex-col sm:flex-row justify-between items-center mt-6 gap-3">
                <button id="generateSubtasksBtn" class="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-fuchsia-600 to-purple-600 text-white rounded-lg hover:from-fuchsia-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50">
                    <span id="subtask-btn-text">✨ Generate Sub-tasks</span>
                    <div id="subtask-spinner" class="spinner hidden"></div>
                </button>
                <div class="flex space-x-3">
                    <button id="cancelTaskBtn" class="px-6 py-3 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition-colors">Cancel</button>
                    <button id="saveTaskBtn" class="btn-primary px-6 py-3 text-white rounded-lg transition-all duration-300">Save Task</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AI Suggestion Modal -->
    <div id="suggestionModal" class="modal fixed inset-0 bg-slate-900 bg-opacity-60 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <h3 class="text-xl font-semibold mb-2 text-slate-800 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-fuchsia-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>AI Suggestion</h3>
            <p class="text-sm text-slate-500 mb-4 ml-8">For task: "<span id="suggestion-task-text" class="font-semibold"></span>"</p>
            <div id="suggestion-content" class="bg-slate-50 p-4 rounded-lg min-h-[60px] text-slate-700">Loading...</div>
            <div class="flex justify-end mt-4">
                <button id="closeSuggestionBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">Close</button>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        // --- Firebase Configuration ---
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, query } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged, browserLocalPersistence, setPersistence } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase config - you need to add your actual config here
        const firebaseConfig = {
            // Add your Firebase config here
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        const appId = 'kanban-tracker';
        const initialAuthToken = null;
        
        let app, db, auth, userId, tasksCollectionRef;
        
        // --- UI Elements ---
        const columns = { todo: document.getElementById('todo'), inprogress: document.getElementById('inprogress'), done: document.getElementById('done') };
        const counts = { todo: document.getElementById('todo-count'), inprogress: document.getElementById('inprogress-count'), done: document.getElementById('done-count') };
        const authStatusEl = document.getElementById('auth-status');
        const userIdDisplayEl = document.getElementById('user-id-display');

        // --- Modal Elements ---
        const taskModal = document.getElementById('taskModal');
        const addTaskBtn = document.getElementById('addTaskBtn');
        const cancelTaskBtn = document.getElementById('cancelTaskBtn');
        const saveTaskBtn = document.getElementById('saveTaskBtn');
        const taskInput = document.getElementById('taskInput');
        const generateSubtasksBtn = document.getElementById('generateSubtasksBtn');
        const subtaskSpinner = document.getElementById('subtask-spinner');
        const subtaskBtnText = document.getElementById('subtask-btn-text');
        
        // --- Suggestion Modal Elements ---
        const suggestionModal = document.getElementById('suggestionModal');
        const suggestionTaskText = document.getElementById('suggestion-task-text');
        const suggestionContent = document.getElementById('suggestion-content');
        const closeSuggestionBtn = document.getElementById('closeSuggestionBtn');

        // --- App State ---
        let tasks = {}; // Local cache of tasks { id: {text, status} }

        // --- Main App Logic ---

        async function initializeFirebase() {
            if (Object.keys(firebaseConfig).length === 0) {
                authStatusEl.textContent = 'Firebase configuration is missing.';
                return;
            }
            try {
                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                auth = getAuth(app);
                await setPersistence(auth, browserLocalPersistence);
                setupAuthListener();
            } catch (error) {
                console.error("Firebase initialization failed:", error);
                authStatusEl.textContent = 'Error connecting to services.';
            }
        }

        function setupAuthListener() {
            onAuthStateChanged(auth, async (user) => {
                if (user) {
                    userId = user.uid;
                    authStatusEl.textContent = 'Connected & Synced';
                    userIdDisplayEl.textContent = userId;
                    tasksCollectionRef = collection(db, `artifacts/${appId}/public/data/tasks`);
                    setupRealtimeListener();
                    initializeSortable();
                } else {
                    authStatusEl.textContent = 'Authenticating...';
                    userIdDisplayEl.textContent = 'N/A';
                    await authenticateUser();
                }
            });
        }

        async function authenticateUser() {
            try {
                if (initialAuthToken) {
                    await signInWithCustomToken(auth, initialAuthToken);
                } else {
                    await signInAnonymously(auth);
                }
            } catch (error) {
                console.error("Authentication failed:", error);
                authStatusEl.textContent = 'Authentication failed.';
            }
        }

        function setupRealtimeListener() {
            if (!tasksCollectionRef) return;
            onSnapshot(query(tasksCollectionRef), (snapshot) => {
                const newTasks = {};
                snapshot.forEach(doc => {
                    newTasks[doc.id] = { ...doc.data(), id: doc.id };
                });
                tasks = newTasks;
                renderBoard();
            });
        }

        function renderBoard() {
            Object.values(columns).forEach(col => col.innerHTML = '');
            Object.values(counts).forEach(count => count.textContent = '0');
            Object.values(tasks).forEach(task => {
                if (columns[task.status]) {
                    const taskElement = createTaskElement(task.id, task.text);
                    columns[task.status].appendChild(taskElement);
                }
            });
            updateCounts();
        }

        function updateCounts() {
            const statusCounts = { todo: 0, inprogress: 0, done: 0 };
            Object.values(tasks).forEach(task => {
                if (statusCounts.hasOwnProperty(task.status)) {
                    statusCounts[task.status]++;
                }
            });
            Object.keys(statusCounts).forEach(status => {
                counts[status].textContent = statusCounts[status];
            });
        }

        function createTaskElement(id, text) {
            const div = document.createElement('div');
            div.dataset.id = id;
            div.className = 'task-card bg-white border border-slate-200 rounded-lg p-3 mb-3 shadow-sm cursor-grab active:cursor-grabbing relative';
            
            const p = document.createElement('p');
            p.textContent = text;
            p.className = 'text-sm text-slate-700 pr-10';

            const controls = document.createElement('div');
            controls.className = 'card-controls absolute top-2 right-2 flex items-center space-x-1';

            const suggestBtn = document.createElement('button');
            suggestBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>`;
            suggestBtn.title = 'Suggest Next Step';
            suggestBtn.className = 'text-slate-400 hover:text-fuchsia-500 transition-colors';
            suggestBtn.onclick = (e) => { e.stopPropagation(); handleSuggestNextStep(text); };

            const deleteBtn = document.createElement('button');
            deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>`;
            deleteBtn.title = 'Delete Task';
            deleteBtn.className = 'text-slate-400 hover:text-rose-500 transition-colors';
            deleteBtn.onclick = (e) => { e.stopPropagation(); deleteTask(id); };

            controls.appendChild(suggestBtn);
            controls.appendChild(deleteBtn);
            div.appendChild(p);
            div.appendChild(controls);
            return div;
        }

        function initializeSortable() {
            Object.values(columns).forEach(col => {
                new Sortable(col, { group: 'kanban', animation: 150, onEnd: handleDragEnd });
            });
        }

        async function handleDragEnd(evt) {
            const taskId = evt.item.dataset.id;
            const newStatus = evt.to.dataset.status;
            const oldStatus = evt.from.dataset.status;

            if (tasks[taskId] && tasks[taskId].status !== newStatus) {
                try {
                    const taskRef = doc(db, `artifacts/${appId}/public/data/tasks/${taskId}`);
                    await updateDoc(taskRef, { status: newStatus });
                    if (newStatus === 'done' && oldStatus !== 'done') triggerConfetti();
                } catch (error) {
                    console.error("Error updating task status:", error);
                    renderBoard(); 
                }
            }
        }
        
        async function addTask(text) {
            if (!text.trim() || !tasksCollectionRef) return;
            try {
                await addDoc(tasksCollectionRef, { text: text.trim(), status: 'todo', createdAt: new Date().toISOString() });
            } catch (error) { console.error("Error adding task:", error); }
        }

        async function deleteTask(id) {
            if (!id) return;
            // NOTE: A custom modal would be better than window.confirm in a real-world app.
            if (window.confirm('Are you sure you want to delete this task?')) {
                try {
                    const taskRef = doc(db, `artifacts/${appId}/public/data/tasks/${id}`);
                    await deleteDoc(taskRef);
                } catch (error) { console.error("Error deleting task:", error); }
            }
        }

        function triggerConfetti() {
            const duration = 2 * 1000, animationEnd = Date.now() + duration;
            const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 100 };
            function randomInRange(min, max) { return Math.random() * (max - min) + min; }
            const interval = setInterval(() => {
                const timeLeft = animationEnd - Date.now();
                if (timeLeft <= 0) return clearInterval(interval);
                const particleCount = 50 * (timeLeft / duration);
                confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 } });
                confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 } });
            }, 250);
        }

        // --- Gemini API Integration ---
        async function callGemini(prompt) {
            const apiKey = ""; // Leave blank
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
            try {
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
                if (!response.ok) throw new Error(`API call failed with status: ${response.status}`);
                const result = await response.json();
                if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                    return result.candidates[0].content.parts[0].text;
                }
                throw new Error("Invalid response structure from API.");
            } catch (error) {
                console.error("Gemini API call failed:", error);
                return null;
            }
        }

        async function handleGenerateSubtasks() {
            const mainTask = taskInput.value.trim();
            if (!mainTask) return;

            generateSubtasksBtn.disabled = true;
            subtaskSpinner.classList.remove('hidden');
            subtaskBtnText.textContent = 'Generating...';

            const prompt = `Break down the following high-level task into a short, numbered list of smaller, actionable sub-tasks. Only provide the list, nothing else. Task: "${mainTask}"`;
            const result = await callGemini(prompt);
            
            if (result) {
                const subtasks = result.split('\n').map(s => s.replace(/^\d+\.\s*/, '').trim()).filter(Boolean);
                for (const subtask of subtasks) { await addTask(subtask); }
                hideModal();
            } else {
                // NOTE: A custom, non-blocking notification would be better than an alert.
                alert("Sorry, I couldn't generate sub-tasks right now. Please try again.");
            }

            generateSubtasksBtn.disabled = false;
            subtaskSpinner.classList.add('hidden');
            subtaskBtnText.textContent = '✨ Generate Sub-tasks';
        }

        async function handleSuggestNextStep(taskText) {
            showSuggestionModal();
            suggestionTaskText.textContent = taskText;
            suggestionContent.innerHTML = `<div class="flex justify-center items-center h-full"><div class="spinner" style="border-left-color: #4f46e5; width: 24px; height: 24px;"></div></div>`;

            const prompt = `Given the task "${taskText}", what is a single, concrete next step to move this task forward? Be concise and actionable.`;
            const result = await callGemini(prompt);

            if (result) {
                suggestionContent.textContent = result;
            } else {
                suggestionContent.textContent = "Could not get a suggestion at this time.";
            }
        }

        // --- Modal Logic ---
        function showModal() {
            taskInput.value = '';
            taskModal.classList.remove('hidden');
            taskInput.focus();
        }
        function hideModal() { taskModal.classList.add('hidden'); }
        
        function showSuggestionModal() { suggestionModal.classList.remove('hidden'); }
        function hideSuggestionModal() { suggestionModal.classList.add('hidden'); }

        addTaskBtn.addEventListener('click', showModal);
        cancelTaskBtn.addEventListener('click', hideModal);
        saveTaskBtn.addEventListener('click', () => {
            if (taskInput.value.trim()) { addTask(taskInput.value); hideModal(); }
        });
        taskInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); saveTaskBtn.click(); }
        });
        
        generateSubtasksBtn.addEventListener('click', handleGenerateSubtasks);
        closeSuggestionBtn.addEventListener('click', hideSuggestionModal);

        // --- App Entry Point ---
        window.onload = initializeFirebase;

    </script>
</body>
</html>
