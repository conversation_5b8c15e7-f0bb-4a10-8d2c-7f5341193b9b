<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Kanban Tracker</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- SortableJS for Drag and Drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    
    <!-- Canvas Confetti for celebrations -->
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>

    <style>
        /* --- Custom Styling for a Modern Look --- */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        /* Kanban column scrollbar styling */
        .kanban-column-body::-webkit-scrollbar { width: 8px; }
        .kanban-column-body::-webkit-scrollbar-track { background: #e2e8f0; border-radius: 10px; }
        .kanban-column-body::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 10px; }
        .kanban-column-body::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Drag-and-drop placeholder style */
        .sortable-ghost {
            background: #dbeafe; /* blue-200 */
            opacity: 0.6;
            border-radius: 0.5rem;
            transform: rotate(2deg);
        }
        .sortable-chosen { cursor: grabbing; }

        /* Improved task card styling */
        .task-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .task-card:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
            border-color: #cbd5e1;
        }
        .task-card .card-controls {
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        .task-card:hover .card-controls {
            opacity: 1;
        }

        /* Priority indicators */
        .priority-high { border-left: 4px solid #ef4444; }
        .priority-medium { border-left: 4px solid #f59e0b; }
        .priority-low { border-left: 4px solid #10b981; }

        /* Better modal styling */
        .modal {
            backdrop-filter: blur(8px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .modal.hidden {
            opacity: 0;
            transform: scale(0.9);
            pointer-events: none;
        }

        /* Spinner for AI actions */
        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border-left-color: #fff;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Improved column headers */
        .kanban-column-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 2px solid #e2e8f0;
            backdrop-filter: blur(10px);
        }

        /* Better button styling */
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
            box-shadow: 0 4px 6px -1px rgb(79 70 229 / 0.1);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #4338ca 0%, #5b21b6 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 12px -2px rgb(79 70 229 / 0.2);
        }

        /* Notification toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }
        .toast.show {
            transform: translateX(0);
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
                color: #e2e8f0;
            }
            .task-card {
                background: rgba(30, 41, 59, 0.95);
                border-color: #334155;
                color: #e2e8f0;
            }
            .kanban-column-header {
                background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
                border-color: #475569;
            }
        }

        /* Animation for task completion */
        @keyframes taskComplete {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .task-completing {
            animation: taskComplete 0.5s ease;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .task-card {
                margin-bottom: 8px;
            }
            .kanban-column-body {
                min-height: 200px;
            }
        }
    </style>
</head>
<body class="bg-slate-100 text-slate-800">

    <div id="app-container" class="p-4 md:p-8 min-h-screen flex flex-col">
        <!-- Header -->
        <header class="mb-8">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-slate-900 dark:text-slate-100">AI-Powered Kanban Tracker</h1>
                    <p id="auth-status" class="text-sm text-slate-500 dark:text-slate-400 mt-1">Connecting...</p>
                </div>
                <button id="addTaskBtn" class="btn-primary mt-4 md:mt-0 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Add New Task
                </button>
            </div>

            <!-- Search and Filter Bar -->
            <div class="flex flex-col sm:flex-row gap-4 bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm border border-slate-200 dark:border-slate-600">
                <div class="flex-1">
                    <input type="text" id="searchInput" placeholder="Search tasks..." class="w-full px-4 py-2 border border-slate-200 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all dark:bg-slate-700 dark:text-slate-200">
                </div>
                <div class="flex gap-2">
                    <select id="priorityFilter" class="px-4 py-2 border border-slate-200 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all dark:bg-slate-700 dark:text-slate-200">
                        <option value="">All Priorities</option>
                        <option value="high">🔴 High</option>
                        <option value="medium">🟡 Medium</option>
                        <option value="low">🟢 Low</option>
                    </select>
                    <button id="clearFiltersBtn" class="px-4 py-2 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-300 dark:hover:bg-slate-500 transition-colors">
                        Clear
                    </button>
                </div>
            </div>
        </header>

        <!-- Kanban Board -->
        <main id="kanban-board" class="flex-grow grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- To Do Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-rose-100 text-rose-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">To Do</span>
                            <span id="todo-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="todo" data-status="todo" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
            <!-- In Progress Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">In Progress</span>
                            <span id="inprogress-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="inprogress" data-status="inprogress" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
            <!-- Done Column -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col overflow-hidden">
                <div class="kanban-column-header p-4">
                    <h2 class="text-lg font-semibold flex items-center justify-between text-slate-700">
                        <div class="flex items-center">
                            <span class="bg-emerald-100 text-emerald-700 text-xs font-bold mr-3 px-3 py-1.5 rounded-full">Done</span>
                            <span id="done-count" class="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">0</span>
                        </div>
                    </h2>
                </div>
                <div id="done" data-status="done" class="kanban-column-body p-4 flex-grow overflow-y-auto min-h-[300px]"></div>
            </div>
        </main>
        
        <footer class="text-center text-xs text-slate-400 mt-8">
            <p>Your User ID (for sharing): <span id="user-id-display" class="font-mono bg-slate-200 text-slate-600 p-1 rounded dark:bg-slate-700 dark:text-slate-300"></span></p>
        </footer>
    </div>

    <!-- Notification Toast -->
    <div id="toast" class="toast">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <div id="toast-title" class="font-semibold text-slate-800"></div>
                <div id="toast-message" class="text-sm text-slate-600"></div>
            </div>
            <button id="toast-close" class="ml-auto text-slate-400 hover:text-slate-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Enhanced Task Modal -->
    <div id="taskModal" class="modal fixed inset-0 bg-slate-900/60 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-2xl p-6 w-full max-w-lg mx-4 border border-slate-200 dark:bg-slate-800 dark:border-slate-600">
            <h3 class="text-2xl font-bold mb-6 text-slate-800 dark:text-slate-200 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create a New Task
            </h3>

            <div class="space-y-4">
                <div>
                    <label for="taskInput" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Task Description</label>
                    <textarea id="taskInput" class="w-full border-2 border-slate-200 dark:border-slate-600 rounded-lg p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all resize-none dark:bg-slate-700 dark:text-slate-200" rows="4" placeholder="Enter a task description or a big goal to break down..."></textarea>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="prioritySelect" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Priority</label>
                        <select id="prioritySelect" class="w-full border-2 border-slate-200 dark:border-slate-600 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all dark:bg-slate-700 dark:text-slate-200">
                            <option value="low">🟢 Low</option>
                            <option value="medium" selected>🟡 Medium</option>
                            <option value="high">🔴 High</option>
                        </select>
                    </div>

                    <div>
                        <label for="dueDateInput" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Due Date (Optional)</label>
                        <input type="date" id="dueDateInput" class="w-full border-2 border-slate-200 dark:border-slate-600 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all dark:bg-slate-700 dark:text-slate-200">
                    </div>
                </div>

                <div>
                    <label for="tagsInput" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Tags (comma-separated)</label>
                    <input type="text" id="tagsInput" class="w-full border-2 border-slate-200 dark:border-slate-600 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all dark:bg-slate-700 dark:text-slate-200" placeholder="e.g., urgent, work, personal">
                </div>
            </div>

            <div class="flex flex-col sm:flex-row justify-between items-center mt-6 gap-3">
                <button id="generateSubtasksBtn" class="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-fuchsia-600 to-purple-600 text-white rounded-lg hover:from-fuchsia-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50">
                    <span id="subtask-btn-text">✨ Generate Sub-tasks</span>
                    <div id="subtask-spinner" class="spinner hidden"></div>
                </button>
                <div class="flex space-x-3">
                    <button id="cancelTaskBtn" class="px-6 py-3 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition-colors dark:bg-slate-600 dark:text-slate-200 dark:hover:bg-slate-500">Cancel</button>
                    <button id="saveTaskBtn" class="btn-primary px-6 py-3 text-white rounded-lg transition-all duration-300">Save Task</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AI Suggestion Modal -->
    <div id="suggestionModal" class="modal fixed inset-0 bg-slate-900 bg-opacity-60 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <h3 class="text-xl font-semibold mb-2 text-slate-800 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-fuchsia-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>AI Suggestion</h3>
            <p class="text-sm text-slate-500 mb-4 ml-8">For task: "<span id="suggestion-task-text" class="font-semibold"></span>"</p>
            <div id="suggestion-content" class="bg-slate-50 p-4 rounded-lg min-h-[60px] text-slate-700">Loading...</div>
            <div class="flex justify-end mt-4">
                <button id="closeSuggestionBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">Close</button>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        // --- Firebase Configuration ---
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, query } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged, browserLocalPersistence, setPersistence } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase config - you need to add your actual config here
        const firebaseConfig = {
            // Add your Firebase config here
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        const appId = 'kanban-tracker';
        const initialAuthToken = null;
        
        let app, db, auth, userId, tasksCollectionRef;
        
        // --- UI Elements ---
        const columns = { todo: document.getElementById('todo'), inprogress: document.getElementById('inprogress'), done: document.getElementById('done') };
        const counts = { todo: document.getElementById('todo-count'), inprogress: document.getElementById('inprogress-count'), done: document.getElementById('done-count') };
        const authStatusEl = document.getElementById('auth-status');
        const userIdDisplayEl = document.getElementById('user-id-display');

        // --- Modal Elements ---
        const taskModal = document.getElementById('taskModal');
        const addTaskBtn = document.getElementById('addTaskBtn');
        const cancelTaskBtn = document.getElementById('cancelTaskBtn');
        const saveTaskBtn = document.getElementById('saveTaskBtn');
        const taskInput = document.getElementById('taskInput');
        const prioritySelect = document.getElementById('prioritySelect');
        const dueDateInput = document.getElementById('dueDateInput');
        const tagsInput = document.getElementById('tagsInput');
        const generateSubtasksBtn = document.getElementById('generateSubtasksBtn');
        const subtaskSpinner = document.getElementById('subtask-spinner');
        const subtaskBtnText = document.getElementById('subtask-btn-text');

        // --- Toast Elements ---
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');
        const toastClose = document.getElementById('toast-close');

        // --- Search and Filter Elements ---
        const searchInput = document.getElementById('searchInput');
        const priorityFilter = document.getElementById('priorityFilter');
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        
        // --- Suggestion Modal Elements ---
        const suggestionModal = document.getElementById('suggestionModal');
        const suggestionTaskText = document.getElementById('suggestion-task-text');
        const suggestionContent = document.getElementById('suggestion-content');
        const closeSuggestionBtn = document.getElementById('closeSuggestionBtn');

        // --- App State ---
        let tasks = {}; // Local cache of tasks { id: {text, status, priority, dueDate, tags} }
        let filteredTasks = {}; // Filtered tasks based on search and filters

        // --- Notification System ---
        function showToast(title, message, type = 'info') {
            const icons = {
                success: '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
                error: '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
                warning: '<svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
                info: '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
            };

            toastIcon.innerHTML = icons[type] || icons.info;
            toastTitle.textContent = title;
            toastMessage.textContent = message;
            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, 5000);
        }

        toastClose.addEventListener('click', () => {
            toast.classList.remove('show');
        });

        // --- Search and Filter Logic ---
        function applyFilters() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            const priorityFilterValue = priorityFilter.value;

            filteredTasks = {};

            Object.entries(tasks).forEach(([id, task]) => {
                let matches = true;

                // Search filter
                if (searchTerm) {
                    const searchableText = [
                        task.text,
                        ...(task.tags || [])
                    ].join(' ').toLowerCase();

                    matches = matches && searchableText.includes(searchTerm);
                }

                // Priority filter
                if (priorityFilterValue) {
                    matches = matches && task.priority === priorityFilterValue;
                }

                if (matches) {
                    filteredTasks[id] = task;
                }
            });

            renderBoard();
        }

        function clearFilters() {
            searchInput.value = '';
            priorityFilter.value = '';
            applyFilters();
        }

        // Event listeners for search and filter
        searchInput.addEventListener('input', applyFilters);
        priorityFilter.addEventListener('change', applyFilters);
        clearFiltersBtn.addEventListener('click', clearFilters);

        // --- Main App Logic ---

        async function initializeFirebase() {
            if (Object.keys(firebaseConfig).length === 0) {
                authStatusEl.textContent = 'Firebase configuration is missing.';
                return;
            }
            try {
                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                auth = getAuth(app);
                await setPersistence(auth, browserLocalPersistence);
                setupAuthListener();
            } catch (error) {
                console.error("Firebase initialization failed:", error);
                authStatusEl.textContent = 'Error connecting to services.';
            }
        }

        function setupAuthListener() {
            onAuthStateChanged(auth, async (user) => {
                if (user) {
                    userId = user.uid;
                    authStatusEl.textContent = 'Connected & Synced';
                    userIdDisplayEl.textContent = userId;
                    tasksCollectionRef = collection(db, `artifacts/${appId}/public/data/tasks`);
                    setupRealtimeListener();
                    initializeSortable();
                } else {
                    authStatusEl.textContent = 'Authenticating...';
                    userIdDisplayEl.textContent = 'N/A';
                    await authenticateUser();
                }
            });
        }

        async function authenticateUser() {
            try {
                if (initialAuthToken) {
                    await signInWithCustomToken(auth, initialAuthToken);
                } else {
                    await signInAnonymously(auth);
                }
            } catch (error) {
                console.error("Authentication failed:", error);
                authStatusEl.textContent = 'Authentication failed.';
            }
        }

        function setupRealtimeListener() {
            if (!tasksCollectionRef) return;
            onSnapshot(query(tasksCollectionRef), (snapshot) => {
                const newTasks = {};
                snapshot.forEach(doc => {
                    newTasks[doc.id] = { ...doc.data(), id: doc.id };
                });
                tasks = newTasks;
                applyFilters(); // This will call renderBoard() with filtered results
            });
        }

        function renderBoard() {
            Object.values(columns).forEach(col => col.innerHTML = '');
            Object.values(counts).forEach(count => count.textContent = '0');

            // Use filtered tasks if filters are applied, otherwise use all tasks
            const tasksToRender = Object.keys(filteredTasks).length > 0 || searchInput.value || priorityFilter.value
                ? filteredTasks
                : tasks;

            // Sort tasks by priority and due date
            const sortedTasks = Object.values(tasksToRender).sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                const aPriority = priorityOrder[a.priority] || 2;
                const bPriority = priorityOrder[b.priority] || 2;

                if (aPriority !== bPriority) {
                    return bPriority - aPriority; // Higher priority first
                }

                // If same priority, sort by due date
                if (a.dueDate && b.dueDate) {
                    return new Date(a.dueDate) - new Date(b.dueDate);
                } else if (a.dueDate) {
                    return -1; // Tasks with due dates come first
                } else if (b.dueDate) {
                    return 1;
                }

                return 0;
            });

            sortedTasks.forEach(task => {
                if (columns[task.status]) {
                    const taskElement = createTaskElement(task.id, task);
                    columns[task.status].appendChild(taskElement);
                }
            });
            updateCounts(tasksToRender);
        }

        function updateCounts(tasksToCount = tasks) {
            const statusCounts = { todo: 0, inprogress: 0, done: 0 };
            Object.values(tasksToCount).forEach(task => {
                if (statusCounts.hasOwnProperty(task.status)) {
                    statusCounts[task.status]++;
                }
            });
            Object.keys(statusCounts).forEach(status => {
                counts[status].textContent = statusCounts[status];
            });
        }

        function createTaskElement(id, taskData) {
            const { text, priority = 'medium', dueDate, tags = [] } = taskData;
            const div = document.createElement('div');
            div.dataset.id = id;

            let priorityClass = '';
            switch(priority) {
                case 'high': priorityClass = 'priority-high'; break;
                case 'medium': priorityClass = 'priority-medium'; break;
                case 'low': priorityClass = 'priority-low'; break;
            }

            div.className = `task-card bg-white border border-slate-200 rounded-lg p-3 mb-3 shadow-sm cursor-grab active:cursor-grabbing relative ${priorityClass}`;

            // Task content container
            const content = document.createElement('div');
            content.className = 'pr-10';

            // Task text
            const p = document.createElement('p');
            p.textContent = text;
            p.className = 'text-sm text-slate-700 mb-2 dark:text-slate-300';
            content.appendChild(p);

            // Task metadata
            const metadata = document.createElement('div');
            metadata.className = 'flex flex-wrap gap-2 text-xs';

            // Priority badge
            const priorityBadge = document.createElement('span');
            const priorityColors = {
                high: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300',
                medium: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
                low: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
            };
            priorityBadge.className = `px-2 py-1 rounded-full ${priorityColors[priority]}`;
            priorityBadge.textContent = priority.charAt(0).toUpperCase() + priority.slice(1);
            metadata.appendChild(priorityBadge);

            // Due date
            if (dueDate) {
                const dueBadge = document.createElement('span');
                const isOverdue = new Date(dueDate) < new Date();
                dueBadge.className = `px-2 py-1 rounded-full ${isOverdue ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300' : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'}`;
                dueBadge.textContent = `Due: ${new Date(dueDate).toLocaleDateString()}`;
                metadata.appendChild(dueBadge);
            }

            // Tags
            if (tags && tags.length > 0) {
                tags.forEach(tag => {
                    const tagBadge = document.createElement('span');
                    tagBadge.className = 'px-2 py-1 rounded-full bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-400';
                    tagBadge.textContent = `#${tag.trim()}`;
                    metadata.appendChild(tagBadge);
                });
            }

            if (metadata.children.length > 0) {
                content.appendChild(metadata);
            }

            // Controls
            const controls = document.createElement('div');
            controls.className = 'card-controls absolute top-2 right-2 flex items-center space-x-1';

            const suggestBtn = document.createElement('button');
            suggestBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>`;
            suggestBtn.title = 'Suggest Next Step';
            suggestBtn.className = 'text-slate-400 hover:text-fuchsia-500 transition-colors';
            suggestBtn.onclick = (e) => { e.stopPropagation(); handleSuggestNextStep(text); };

            const deleteBtn = document.createElement('button');
            deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>`;
            deleteBtn.title = 'Delete Task';
            deleteBtn.className = 'text-slate-400 hover:text-rose-500 transition-colors';
            deleteBtn.onclick = (e) => { e.stopPropagation(); deleteTask(id); };

            controls.appendChild(suggestBtn);
            controls.appendChild(deleteBtn);
            div.appendChild(content);
            div.appendChild(controls);
            return div;
        }

        function initializeSortable() {
            Object.values(columns).forEach(col => {
                new Sortable(col, { group: 'kanban', animation: 150, onEnd: handleDragEnd });
            });
        }

        async function handleDragEnd(evt) {
            const taskId = evt.item.dataset.id;
            const newStatus = evt.to.dataset.status;
            const oldStatus = evt.from.dataset.status;

            if (tasks[taskId] && tasks[taskId].status !== newStatus) {
                // Add visual feedback
                evt.item.classList.add('task-completing');

                try {
                    const taskRef = doc(db, `artifacts/${appId}/public/data/tasks/${taskId}`);
                    await updateDoc(taskRef, {
                        status: newStatus,
                        updatedAt: new Date().toISOString()
                    });

                    if (newStatus === 'done' && oldStatus !== 'done') {
                        triggerConfetti();
                        showToast('Task Completed!', `"${tasks[taskId].text}" has been completed.`, 'success');
                    } else {
                        const statusNames = { todo: 'To Do', inprogress: 'In Progress', done: 'Done' };
                        showToast('Task Moved', `Task moved to ${statusNames[newStatus]}`, 'info');
                    }
                } catch (error) {
                    console.error("Error updating task status:", error);
                    showToast('Error', 'Failed to update task status. Please try again.', 'error');
                    renderBoard();
                } finally {
                    setTimeout(() => {
                        evt.item.classList.remove('task-completing');
                    }, 500);
                }
            }
        }
        
        async function addTask(text, priority = 'medium', dueDate = null, tags = []) {
            if (!text.trim() || !tasksCollectionRef) return;
            try {
                const taskData = {
                    text: text.trim(),
                    status: 'todo',
                    priority: priority,
                    createdAt: new Date().toISOString()
                };

                if (dueDate) {
                    taskData.dueDate = dueDate;
                }

                if (tags && tags.length > 0) {
                    taskData.tags = tags;
                }

                await addDoc(tasksCollectionRef, taskData);
                showToast('Success', 'Task created successfully!', 'success');
            } catch (error) {
                console.error("Error adding task:", error);
                showToast('Error', 'Failed to create task. Please try again.', 'error');
            }
        }

        async function deleteTask(id) {
            if (!id) return;
            // NOTE: A custom modal would be better than window.confirm in a real-world app.
            if (window.confirm('Are you sure you want to delete this task?')) {
                try {
                    const taskRef = doc(db, `artifacts/${appId}/public/data/tasks/${id}`);
                    await deleteDoc(taskRef);
                } catch (error) { console.error("Error deleting task:", error); }
            }
        }

        function triggerConfetti() {
            const duration = 2 * 1000, animationEnd = Date.now() + duration;
            const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 100 };
            function randomInRange(min, max) { return Math.random() * (max - min) + min; }
            const interval = setInterval(() => {
                const timeLeft = animationEnd - Date.now();
                if (timeLeft <= 0) return clearInterval(interval);
                const particleCount = 50 * (timeLeft / duration);
                confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 } });
                confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 } });
            }, 250);
        }

        // --- Gemini API Integration ---
        async function callGemini(prompt) {
            const apiKey = ""; // Leave blank
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
            try {
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
                if (!response.ok) throw new Error(`API call failed with status: ${response.status}`);
                const result = await response.json();
                if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                    return result.candidates[0].content.parts[0].text;
                }
                throw new Error("Invalid response structure from API.");
            } catch (error) {
                console.error("Gemini API call failed:", error);
                return null;
            }
        }

        async function handleGenerateSubtasks() {
            const mainTask = taskInput.value.trim();
            if (!mainTask) return;

            generateSubtasksBtn.disabled = true;
            subtaskSpinner.classList.remove('hidden');
            subtaskBtnText.textContent = 'Generating...';

            const prompt = `Break down the following high-level task into a short, numbered list of smaller, actionable sub-tasks. Only provide the list, nothing else. Task: "${mainTask}"`;
            const result = await callGemini(prompt);

            if (result) {
                const subtasks = result.split('\n').map(s => s.replace(/^\d+\.\s*/, '').trim()).filter(Boolean);
                const priority = prioritySelect.value;
                const dueDate = dueDateInput.value || null;
                const tags = tagsInput.value ? tagsInput.value.split(',').map(tag => tag.trim()).filter(Boolean) : [];

                for (const subtask of subtasks) {
                    await addTask(subtask, priority, dueDate, tags);
                }
                hideModal();
                showToast('Sub-tasks Generated!', `Created ${subtasks.length} sub-tasks from your main task.`, 'success');
            } else {
                showToast('Generation Failed', "Sorry, I couldn't generate sub-tasks right now. Please try again.", 'error');
            }

            generateSubtasksBtn.disabled = false;
            subtaskSpinner.classList.add('hidden');
            subtaskBtnText.textContent = '✨ Generate Sub-tasks';
        }

        async function handleSuggestNextStep(taskText) {
            showSuggestionModal();
            suggestionTaskText.textContent = taskText;
            suggestionContent.innerHTML = `<div class="flex justify-center items-center h-full"><div class="spinner" style="border-left-color: #4f46e5; width: 24px; height: 24px;"></div></div>`;

            const prompt = `Given the task "${taskText}", what is a single, concrete next step to move this task forward? Be concise and actionable.`;
            const result = await callGemini(prompt);

            if (result) {
                suggestionContent.textContent = result;
            } else {
                suggestionContent.textContent = "Could not get a suggestion at this time.";
            }
        }

        // --- Modal Logic ---
        function showModal() {
            taskInput.value = '';
            prioritySelect.value = 'medium';
            dueDateInput.value = '';
            tagsInput.value = '';
            taskModal.classList.remove('hidden');
            taskInput.focus();
        }

        function hideModal() {
            taskModal.classList.add('hidden');
        }

        function showSuggestionModal() { suggestionModal.classList.remove('hidden'); }
        function hideSuggestionModal() { suggestionModal.classList.add('hidden'); }

        addTaskBtn.addEventListener('click', showModal);
        cancelTaskBtn.addEventListener('click', hideModal);

        saveTaskBtn.addEventListener('click', () => {
            const text = taskInput.value.trim();
            if (text) {
                const priority = prioritySelect.value;
                const dueDate = dueDateInput.value || null;
                const tags = tagsInput.value ? tagsInput.value.split(',').map(tag => tag.trim()).filter(Boolean) : [];

                addTask(text, priority, dueDate, tags);
                hideModal();
            }
        });

        taskInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); saveTaskBtn.click(); }
        });
        
        generateSubtasksBtn.addEventListener('click', handleGenerateSubtasks);
        closeSuggestionBtn.addEventListener('click', hideSuggestionModal);

        // --- App Entry Point ---
        window.onload = initializeFirebase;

    </script>
</body>
</html>
